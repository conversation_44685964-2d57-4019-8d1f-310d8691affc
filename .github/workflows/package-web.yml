name: GitHub Actions
run-name: web
on:
  push:
    branches:
      - main
    paths:
      - packages/web/**
jobs:
  web:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: packages/web
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Install bun
        uses: oven-sh/setup-bun@v2

      - name: Install dependencies
        run: bun install

      - name: Prettier check
        run: bun run prettier --check .

      - name: Lint check
        run: bun run lint

      - name: Run Test
        run: bun run test

      - name: Build app
        run: bun run build
