---
inclusion: fileMatch
fileMatchPattern: "**/*.tsx"
---

# SolidJS Best Practices

## Control Flow Components

Always prioritize using Solid's built-in control flow components over JavaScript's native conditional and iteration methods for better reactivity and performance.

### Conditional Rendering

**Use `Show` instead of ternary operators:**

```tsx
// ❌ Avoid
{
	condition ? <Component /> : <Fallback />
}

// ✅ Prefer
;<Show
	when={condition}
	fallback={<Fallback />}
>
	<Component />
</Show>
```

**Use `Match` and `Switch` for multiple conditions:**

```tsx
// ❌ Avoid
{
	status === "loading" ? <Loading />
	: status === "error" ? <Error />
	: status === "success" ? <Success />
	: null
}

// ✅ Prefer
;<Switch>
	<Match when={status === "loading"}>
		<Loading />
	</Match>
	<Match when={status === "error"}>
		<Error />
	</Match>
	<Match when={status === "success"}>
		<Success />
	</Match>
</Switch>
```

### List Rendering

**Use `For` instead of `.map()`:**

```tsx
// ❌ Avoid
{
	items.map((item) => <Item data={item} />)
}

// ✅ Prefer
;<For each={items}>{(item) => <Item data={item} />}</For>
```

**Use `Index` when you need the index:**

```tsx
// ✅ Use Index when you need both item and index
<Index each={items}>
	{(item, index) => (
		<Item
			data={item()}
			index={index}
		/>
	)}
</Index>
```

## Why Use Control Flow Components?

1. **Better Performance**: Solid's control flow components are optimized for fine-grained reactivity
2. **Cleaner Updates**: Only the necessary parts of the DOM are updated when dependencies change
3. **Type Safety**: Better TypeScript integration and type inference
4. **Readability**: More declarative and easier to understand intent

## Required Imports

Always import the control flow components you need:

```tsx
import { Show, For, Switch, Match, Index } from "solid-js"
```
