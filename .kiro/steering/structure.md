# Project Structure

## Root Level Organization

- **Configuration files**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Script, Vite configs at root
- **Package management**: `package.json`, `bun.lock`, `bunfig.toml`
- **Task automation**: `.justfile` for common development tasks
- **Documentation**: `README.md`, `doc/` folder for guides

## Source Code Structure (`src/`)

### Core Application

- `App.tsx` - Main application component with routing setup
- `index.tsx` - Application entry point
- `index.css` - Global styles and CSS imports

### API Layer (`src/api/`)

- **Domain-based organization**: `artist/`, `auth/`, `release/`, `song/`, `user/`
- **Consistent structure per domain**:
  - `index.ts` - Public exports
  - `fetcher.ts` - HTTP client functions
  - `query.ts` - TanStack Query hooks
  - `mutation.ts` - Mutation hooks (when applicable)
  - `schema.ts` - Validation schemas
  - `type/` - TypeScript type definitions
- `openapi.ts` - Generated OpenAPI client
- `utils.ts` - Shared API utilities

### Components (`src/components/`)

- **Atomic design approach**: Basic UI components
- **Co-located translations**: Each component has `locales/` folder
- **Storybook integration**: `.stories.tsx` files for documentation
- **Categories**:
  - `common/` - Shared base components
  - `composite/` - Complex form components

### Views (`src/views/`)

- **Feature-based organization**: `artist/`, `auth/`, `song/`, `user/`
- **Nested structure**: Each feature can have subviews and components
- **Co-located assets**: Components, data, and utilities grouped by feature

### State Management (`src/state/`)

- **Provider pattern**: Each state concern has its own provider
- **Composition**: All providers composed in main `StateProvider`
- **Domains**: `i18n/`, `theme/`, `user/`, `tanstack/`

### Utilities (`src/utils/`)

- **Functional organization**: `data/`, `dom/`, `error/`, `validate/`
- **Adapters**: External library integrations

### Styling (`src/style/`)

- **Modular CSS**: Separate files for animations, colors, shadows
- **CSS custom properties**: Extensive use of CSS variables
- **Design system**: Consistent color palette and spacing

### Internationalization (`src/locale/`)

- **Language folders**: `en/`, `zh-Hans/`
- **Generated files**: `.po` and `.ts` files per locale
- **Lingui integration**: Automatic extraction and compilation

## Naming Conventions

- **Files**: snake_case for most files, PascalCase for components
- **Folders**: snake_case
- **Components**: PascalCase with descriptive names
- **Utilities**: camelCase functions, descriptive naming

## Import Organization

- **Third-party imports first**
- **Internal imports**: `~/` alias for src root
- **Relative imports**: `../` and `./` patterns
- **Automatic sorting**: Prettier plugin handles import organization

## Co-location Principles

- **Translations**: Each component/view has its own `locales/` folder
- **Tests**: `.test.ts` and `.buntest.ts` files alongside source
- **Stories**: `.stories.tsx` files for Storybook documentation

### Types

- Use `T[]` syntax instead of `Array<T>` for array types
- Use `Record<K, V>` instead of `{ [key: K]: V }` for object types
