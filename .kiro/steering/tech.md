# Tech Stack

## Core Technologies
- **Frontend Framework**: SolidJS with TypeScript
- **Build Tool**: Vite
- **Package Manager**: Bun
- **Task Runner**: Just

## Key Libraries
- **Routing**: TanStack Router with auto code splitting
- **State Management**: TanStack Query for server state
- **Styling**: TailwindCSS v4 with custom CSS variables
- **UI Components**: <PERSON><PERSON><PERSON> (headless components)
- **Forms**: Modular Forms for SolidJS
- **Internationalization**: Lingui with macro support
- **Validation**: Valibot
- **Icons**: Heroicons, Radix Icons
- **Markdown**: Marked with Shiki syntax highlighting

## Development Tools
- **Linting**: ESLint + Oxlint (fast Rust-based linter)
- **Formatting**: Prettier
- **Testing**: Vitest with browser testing, Bun test runner
- **Storybook**: Component development and documentation
- **Git Hooks**: <PERSON><PERSON> for pre-push validation

## Common Commands

### Development
```bash
bun run dev          # Start development server (port 3000)
bun run build        # Production build
bun run serve        # Preview production build
```

### Code Quality
```bash
just lint            # Run both oxlint and eslint
just fix             # Auto-fix linting issues
just fmt             # Format code with prettier
bun run check        # Check code formatting
```

### Testing
```bash
bun run test         # Run all tests
bun run test::vite   # Run Vitest tests
bun run test::bun    # Run Bun tests
```

### Internationalization
```bash
just i18n            # Extract and compile translations
```

### Storybook
```bash
bun run storybook    # Start Storybook dev server
```

## Environment Variables
- `VITE_SERVER_URL`: **Required** - Backend API server URL for OpenAPI generation and proxying
