{
	"$schema": "./node_modules/oxlint/configuration_schema.json",
	"env": {
		"browser": true
	},
	"ignorePatterns": ["src/locale/**"],
	"categories": {
		"correctness": "error",
		"suspicious": "warn",
		"pedantic": "warn",
		"perf": "warn",
		"style": "warn",
		"restriction": "warn",
		"nursery": "warn"
	},
	"settings": {},
	"rules": {
		// False Positives
		"arrow-body-style": "off",
		"array-callback-return": ["off", { "allowImplicitReturn": true }],
		"curly": "off",
		"id-length": "off",
		"max-params": "off",
		"eqeqeq": "off",
		"no-plusplus": "off",
		"no-async-await": "off",
		"no-duplicate-imports": "off",
		"no-ternary": "off",
		"no-undefined": "off",
		"no-unused-vars": "warn",
		"no-magic-numbers": [
			"error",
			{ "ignore": [-1, 0, 1], "ignoreArrayIndexes": true }
		],
		"no-void": "off",
		"no-case-declarations": "off",
		"no-else-return": "off",
		"no-negated-condition": "off",
		"no-empty-function": [
			"warn",
			{
				"allow": ["constructor", "privateConstructors", "protectedConstructors"]
			}
		],
		"max-lines": [
			"warn",
			{
				"max": 300,
				"skipBlankLines": true,
				"skipComments": true
			}
		],
		"sort-keys": "off",

		"default-case": [
			"warn",
			{
				"commentPattern": "Unreachable"
			}
		],

		"oxc/no-const-enum": "off",
		"oxc/no-optional-chaining": "off",
		"oxc/no-barrel-file": "off",
		"oxc/no-rest-spread-properties": "off",

		"eslint/func-style": "off",
		"eslint/max-depth": ["warn", 4],
		"eslint/max-lines-per-function": [
			"warn",
			{
				"IIFEs": false,
				"max": 100,
				"skipBlankLines": false,
				"skipComments": false
			}
		],
		"eslint/max-nested-callbacks": ["warn", 10],
		"eslint/no-redeclare": "off",
		"eslint/sort-imports": "off",
		"eslint/yoda": "off",

		"typescript/ban-ts-comment": "off",
		"typescript/consistent-type-definitions": "off",
		"typescript/explicit-function-return-type": "off",
		"typescript/consistent-indexed-object-style": "warn",
		"typescript/prefer-enum-initializers": "off",
		"typescript/no-non-null-assertion": "off",

		"import/exports-last": "off",
		"import/group-exports": "off",
		"import/max-dependencies": "off",
		"import/no-anonymous-default-export": "off",
		"import/no-cycle": "off",
		"import/no-namespace": "off",
		"import/prefer-default-export": "off",

		"unicorn/no-anonymous-default-export": "off",
		"unicorn/no-empty-file": "off",
		"unicorn/no-null": "off",
		"unicorn/filename-case": [
			"off",
			{
				"cases": {
					"snakeCase": true,
					"pascalCase": true
				}
			}
		],
		"unicorn/switch-case-braces": "off",

		"promise/prefer-await-to-then": "off",

		"jsx-a11y/label-has-associated-control": "off"
	},
	"plugins": [
		"import",
		"jsdoc",
		"jsx-a11y",
		"oxc",
		"promise",
		"typescript",
		"unicorn",
		"vitest"
	],
	"overrides": [
		{
			"files": ["*.stories.tsx"],
			"rules": {
				"import/no-default-export": "off"
			}
		}
	]
}
