# Agent Guidelines for TouhouCloudDB Web Project

## Code Style Guidelines

- TypeScript with strict type checking
- Use tabs for indentation
- No semicolons
- Place each attribute on its own line in JSX/TSX
- Use SolidJS
  - Don't destruct props
- Use type imports with `import type` syntax
- Use Tailwind CSS for styling

- Use lingui-solid for i18n https://github.com/Azq2/js-lingui-solid

  - wrap JSX text in `Trans` component and string literal in `t` function,
    for example:

    ```tsx
    import { createEffect } from "solid-js";
    import { useLingui, Trans } from "@lingui-solid/solid/macro";

    const CurrentLocale = () => {
    const { t, i18n } = useLingui();

    createEffect(() => console.log(`Language chnaged: ${i18n().locale}`));

    return (
    	<span>
    		{t`Current locale`}: {i18n().locale}<br />
    		<Trans>
    		See for more info:
    		<a href="https://lingui.dev/introduction">official documentation</a>
    		</Trans>;
    	</span>
    );

    ```
