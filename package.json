{"name": "@touhouclouddb/web", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "vite build", "serve": "vite preview", "check": "prettier --check ./src", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@floating-ui/dom": "^1.6.13", "@formisch/solid": "^0.1.1", "@kobalte/core": "^0.13.9", "@lingui-solid/solid": "^5.1.4", "@modular-forms/solid": "^0.25.1", "@shikijs/markdown-it": "^3.3.0", "@shikijs/transformers": "^3.3.0", "@solid-primitives/destructure": "^0.2.0", "@solid-primitives/i18n": "^2.2.1", "@solidjs/meta": "^0.29.4", "@solidjs/router": "^0.15.3", "@tanstack/solid-query": "^5.74.4", "@tanstack/solid-query-devtools": "^5.74.7", "@tanstack/solid-router": "^1.117.1", "arktype": "^2.1.20", "cropperjs": "^2.0.0", "dayjs": "^1.11.13", "marked": "^16.0.0", "marked-footnote": "^1.2.4", "marked-shiki": "^1.2.0", "openapi-fetch": "^0.14.0", "radash": "^12.1.1", "shiki": "^3.3.0", "solid-floating-ui": "^0.3.1", "solid-js": "^1.9.5", "solid-popper": "^0.3.0", "solid-radix-icons": "^1.0.0", "tailwind-merge": "^3.2.0", "valibot": "1.1.0"}, "devDependencies": {"@ark/attest": "^0.48.0", "@chromatic-com/storybook": "^3.2.6", "@eslint/js": "^9.21.0", "@faker-js/faker": "^9.5.1", "@lingui-solid/babel-plugin-extract-messages": "^5.1.3", "@lingui-solid/babel-plugin-lingui-macro": "^5.1.3", "@lingui-solid/vite-plugin": "^5.1.3", "@lingui/cli": "^5.3.1", "@lingui/conf": "^5.3.1", "@lingui/core": "^5.3.1", "@lingui/macro": "^5.3.1", "@solidjs/testing-library": "^0.8.10", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/blocks": "^8.6.3", "@storybook/test": "^8.6.12", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.6", "@tanstack/eslint-plugin-query": "^5.74.7", "@tanstack/router-plugin": "^1.117.2", "@tanstack/solid-router-devtools": "^1.117.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/eslint-config-prettier": "^6.11.3", "@types/markdown-it": "^14.1.2", "@types/markdown-it-footnote": "^3.0.4", "@vitest/browser": "^3.1.2", "babel-preset-solid": "^1.9.5", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-oxlint": "^1.0.0", "eslint-plugin-perfectionist": "^4.12.3", "eslint-plugin-solid": "^0.14.5", "eslint-plugin-storybook": "^0.12.0", "globals": "^16.0.0", "hotscript": "^1.0.13", "husky": "^9.1.7", "jsdom": "^26.1.0", "openapi-typescript": "^7.6.1", "oxlint": "^1.0.0", "playwright": "^1.52.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "solid-devtools": "^0.34.0", "storybook": "^8.6.12", "storybook-solidjs": "=1.0.0-beta.6", "storybook-solidjs-vite": "=1.0.0-beta.6", "tailwindcss": "^4.1.6", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^7.0.0", "vite-plugin-babel-macros": "^1.0.6", "vite-plugin-solid": "^2.11.6", "vite-tsconfig-paths": "^5.1.4"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "trustedDependencies": ["@tailwindcss/oxide", "esbuild"]}