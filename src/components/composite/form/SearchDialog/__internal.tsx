import type { JSX } from "solid-js"
import { mergeProps } from "solid-js"
import { MagnifyingGlassIcon } from "solid-radix-icons"
import { twMerge } from "tailwind-merge"

import { Dialog } from "~/components/dialog"

export { Root } from "~/components/dialog/__internal"
export type { RootProps } from "~/components/dialog/__internal"

export type LabelProps = {
	children: JSX.Element
	class?: string
}

export function Label(props: LabelProps) {
	return (
		<Dialog.Title
			class={twMerge(
				"text-3xl font-extralight tracking-tight text-primary",
				props.class,
			)}
		>
			{props.children}
		</Dialog.Title>
	)
}

export type InputProps = JSX.InputHTMLAttributes<HTMLInputElement>

const INPUT_CLASS = `
pl-7 py-1
border-b border-slate-400 hover:border-reimu-600 focus:border-reimu-600
placeholder:font-light placeholder:text-tertiary outline-none
tracking-tighter
transition-all duration-150
`

export function Input(props: InputProps) {
	const inputProps = mergeProps(props, {
		get class() {
			return twMerge(INPUT_CLASS, props.class)
		},
	})
	return (
		<div class="relative">
			<MagnifyingGlassIcon class="absolute z-10 mx-auto size-4 w-8 self-center text-tertiary" />
			<input {...inputProps} />
		</div>
	)
}

const CONTENT_CLASS =
	"flex min-h-128 flex-col h-192 w-128 max-w-[90vw] rounded p-6"

export function Content(props: Dialog.ContentProps) {
	const finalProps = mergeProps(props, {
		get class() {
			return props.class ? twMerge(CONTENT_CLASS, props.class) : CONTENT_CLASS
		},
	})
	return (
		<Dialog.Portal>
			<Dialog.Overlay />
			<Dialog.Content {...finalProps} />
		</Dialog.Portal>
	)
}

export type ListProps = JSX.HTMLAttributes<HTMLUListElement>
const LIST_CLASS = "overflow-auto has-first:border-y border-slate-300 mb-4"
export function List(props: ListProps) {
	const listProps = mergeProps(props, {
		get class() {
			return props.class ? twMerge(LIST_CLASS, props.class) : LIST_CLASS
		},
	})
	return <ul {...listProps}></ul>
}
