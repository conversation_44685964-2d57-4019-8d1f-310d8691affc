msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-18 07:19+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: en\n"
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: \n"
"Last-Translator: \n"
"Language-Team: \n"
"Plural-Forms: \n"

#: src/views/artist/edit/index.tsx:239
msgid "-- Please select artist type --"
msgstr "-- Please select artist type --"

#: src/views/artist/profile/comp/ArtistInfo.tsx:58
msgid "Aliases"
msgstr "Aliases"

#: src/views/artist/edit/index.tsx:147
msgid "Are you sure you want to leave this page? Your changes will be lost."
msgstr "Are you sure you want to leave this page? Your changes will be lost."

#: src/components/Header/LeftSidebar.tsx:44
msgid "Artist"
msgstr "Artist"

#: src/views/artist/edit/index.tsx:230
msgid "Artist Type"
msgstr "Artist Type"

#: src/views/artist/profile/comp/ArtistInfo.tsx:27
msgid "Born"
msgstr "Born"

#: src/components/Header/LeftSidebar.tsx:32
msgid "Chart"
msgstr "Chart"

#: src/views/artist/edit/index.tsx:57
msgid "Create Artist"
msgstr "Create Artist"

#: src/views/artist/edit/index.tsx:292
msgid "Current Location"
msgstr "Current Location"

#: src/views/artist/edit/index.tsx:306
msgid "Description"
msgstr "Description"

#: src/views/artist/profile/comp/ArtistInfo.tsx:32
msgid "Died"
msgstr "Died"

#: src/views/artist/profile/comp/ArtistInfo.tsx:32
msgid "Disbanded"
msgstr "Disbanded"

#: src/components/Header/LeftSidebar.tsx:60
msgid "Discover"
msgstr "Discover"

#: src/views/artist/edit/index.tsx:55
msgid "Edit Artist"
msgstr "Edit Artist"

#: src/views/artist/edit/index.tsx:276
msgid "End date"
msgstr "End date"

#. placeholder {0}: mutation.error.message
#: src/views/artist/edit/index.tsx:352
msgid "Error: {0}"
msgstr "Error: {0}"

#: src/components/Header/LeftSidebar.tsx:48
msgid "Event"
msgstr "Event"

#: src/views/artist/profile/comp/ArtistInfo.tsx:27
msgid "Formed"
msgstr "Formed"

#: src/views/artist/edit/index.tsx:345
msgid "Loading"
msgstr "Loading"

#: src/views/artist/edit/index.tsx:65
msgid "Loading..."
msgstr "Loading..."

#: src/views/artist/profile/comp/ArtistInfo.tsx:104
msgid "Member Of"
msgstr "Member Of"

#: src/views/artist/profile/comp/ArtistInfo.tsx:106
msgid "Members"
msgstr "Members"

#: src/views/artist/edit/index.tsx:210
msgid "Name"
msgstr "Name"

#: src/views/auth/component/AuthForm.tsx:72
msgid "Password"
msgstr "Password"

#: src/components/Header/LeftSidebar.tsx:28
msgid "Recommandation"
msgstr "Recommandation"

#: src/components/Header/LeftSidebar.tsx:40
msgid "Release"
msgstr "Release"

#: src/views/auth/component/AuthForm.tsx:86
msgid "Repeated Password"
msgstr "Repeated Password"

#: src/views/auth/component/AuthForm.tsx:36
#: src/views/auth/component/AuthForm.tsx:104
#: src/components/Header/index.tsx:153
msgid "Sign In"
msgstr "Sign In"

#: src/views/auth/component/AuthForm.tsx:44
#: src/views/auth/component/AuthForm.tsx:104
#: src/components/Header/index.tsx:167
msgid "Sign Up"
msgstr "Sign Up"

#: src/components/Header/LeftSidebar.tsx:36
msgid "Song"
msgstr "Song"

#: src/views/artist/edit/index.tsx:265
msgid "Start date"
msgstr "Start date"

#: src/views/artist/edit/index.tsx:286
msgid "Start Location"
msgstr "Start Location"

#: src/views/artist/edit/index.tsx:345
msgid "Submit"
msgstr "Submit"

#: src/components/Header/LeftSidebar.tsx:52
msgid "Tag"
msgstr "Tag"

#: src/views/artist/profile/comp/ArtistReleaseInfo.tsx:133
msgid "This Artist has no releases yet, you can upload them on <0>Upload New Release</0>"
msgstr "This Artist has no releases yet, you can upload them on <0>Upload New Release</0>"

#: src/App.tsx:35
msgid "Touhou Cloud DB"
msgstr "Touhou Cloud DB"

#: src/App.tsx:39
msgid "Touhou Cloud DB is an open doujin music database"
msgstr "Touhou Cloud DB is an open doujin music database"
