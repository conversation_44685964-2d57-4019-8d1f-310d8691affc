:root {
  --altVerWrapperRad: 0.25rem;
}

.altVerWrapper {
  border-radius: var(--altVerWrapperRad);
}

.altVerBottomButton {
  border-bottom-left-radius: var(--altVerWrapperRad);
  border-bottom-right-radius: var(--altVerWrapperRad);
}

.altVerItem {
  border-radius: max(
    calc(var(--altVerWrapperRad) - calc(0.5rem * 0.5)),
    0.125rem
  );
}
.ratingItem:focus-visible {
  outline: 0.1rem solid;
  border-radius: 0.1rem;
}

.ratingItem:focus {
  outline: transparent;
  border-radius: 0.1rem;
}

.infoRow {
  @apply grid grid-cols-3 gap-1;
  ul {
    @apply flex auto-rows-auto flex-wrap text-nowrap;
  }
}

.vote {
  @apply text-gray-400;
  @apply hover:text-gray-500;
  justify-self: center;
}

main {
  @apply px-4 py-1;
}
