/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LibraryRouteImport } from './routes/library'
import { Route as FeedsRouteImport } from './routes/feeds'
import { Route as AuthRouteImport } from './routes/auth'
import { Route as ArticlesRouteImport } from './routes/articles'
import { Route as AboutRouteImport } from './routes/about'
import { Route as IndexRouteImport } from './routes/index'
import { Route as SongMockRouteImport } from './routes/song/mock'
import { Route as SongIdRouteImport } from './routes/song/$id'
import { Route as ArtistNewRouteImport } from './routes/artist/new'
import { Route as userTest_avatar_uploadRouteImport } from './routes/(user)/test_avatar_upload'
import { Route as userProfileRouteImport } from './routes/(user)/profile'
import { Route as ArtistIdIndexRouteImport } from './routes/artist/$id.index'
import { Route as ArtistIdEditRouteImport } from './routes/artist/$id.edit'
import { Route as userProfileEditRouteImport } from './routes/(user)/profile_.edit'
import { Route as userProfileUsernameRouteImport } from './routes/(user)/profile_.$username'

const LibraryRoute = LibraryRouteImport.update({
  id: '/library',
  path: '/library',
  getParentRoute: () => rootRouteImport,
} as any)
const FeedsRoute = FeedsRouteImport.update({
  id: '/feeds',
  path: '/feeds',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const ArticlesRoute = ArticlesRouteImport.update({
  id: '/articles',
  path: '/articles',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const SongMockRoute = SongMockRouteImport.update({
  id: '/song/mock',
  path: '/song/mock',
  getParentRoute: () => rootRouteImport,
} as any)
const SongIdRoute = SongIdRouteImport.update({
  id: '/song/$id',
  path: '/song/$id',
  getParentRoute: () => rootRouteImport,
} as any)
const ArtistNewRoute = ArtistNewRouteImport.update({
  id: '/artist/new',
  path: '/artist/new',
  getParentRoute: () => rootRouteImport,
} as any)
const userTest_avatar_uploadRoute = userTest_avatar_uploadRouteImport.update({
  id: '/(user)/test_avatar_upload',
  path: '/test_avatar_upload',
  getParentRoute: () => rootRouteImport,
} as any)
const userProfileRoute = userProfileRouteImport.update({
  id: '/(user)/profile',
  path: '/profile',
  getParentRoute: () => rootRouteImport,
} as any)
const ArtistIdIndexRoute = ArtistIdIndexRouteImport.update({
  id: '/artist/$id/',
  path: '/artist/$id/',
  getParentRoute: () => rootRouteImport,
} as any)
const ArtistIdEditRoute = ArtistIdEditRouteImport.update({
  id: '/artist/$id/edit',
  path: '/artist/$id/edit',
  getParentRoute: () => rootRouteImport,
} as any)
const userProfileEditRoute = userProfileEditRouteImport.update({
  id: '/(user)/profile_/edit',
  path: '/profile/edit',
  getParentRoute: () => rootRouteImport,
} as any)
const userProfileUsernameRoute = userProfileUsernameRouteImport.update({
  id: '/(user)/profile_/$username',
  path: '/profile/$username',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/articles': typeof ArticlesRoute
  '/auth': typeof AuthRoute
  '/feeds': typeof FeedsRoute
  '/library': typeof LibraryRoute
  '/profile': typeof userProfileRoute
  '/test_avatar_upload': typeof userTest_avatar_uploadRoute
  '/artist/new': typeof ArtistNewRoute
  '/song/$id': typeof SongIdRoute
  '/song/mock': typeof SongMockRoute
  '/profile/$username': typeof userProfileUsernameRoute
  '/profile/edit': typeof userProfileEditRoute
  '/artist/$id/edit': typeof ArtistIdEditRoute
  '/artist/$id': typeof ArtistIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/articles': typeof ArticlesRoute
  '/auth': typeof AuthRoute
  '/feeds': typeof FeedsRoute
  '/library': typeof LibraryRoute
  '/profile': typeof userProfileRoute
  '/test_avatar_upload': typeof userTest_avatar_uploadRoute
  '/artist/new': typeof ArtistNewRoute
  '/song/$id': typeof SongIdRoute
  '/song/mock': typeof SongMockRoute
  '/profile/$username': typeof userProfileUsernameRoute
  '/profile/edit': typeof userProfileEditRoute
  '/artist/$id/edit': typeof ArtistIdEditRoute
  '/artist/$id': typeof ArtistIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/articles': typeof ArticlesRoute
  '/auth': typeof AuthRoute
  '/feeds': typeof FeedsRoute
  '/library': typeof LibraryRoute
  '/(user)/profile': typeof userProfileRoute
  '/(user)/test_avatar_upload': typeof userTest_avatar_uploadRoute
  '/artist/new': typeof ArtistNewRoute
  '/song/$id': typeof SongIdRoute
  '/song/mock': typeof SongMockRoute
  '/(user)/profile_/$username': typeof userProfileUsernameRoute
  '/(user)/profile_/edit': typeof userProfileEditRoute
  '/artist/$id/edit': typeof ArtistIdEditRoute
  '/artist/$id/': typeof ArtistIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/articles'
    | '/auth'
    | '/feeds'
    | '/library'
    | '/profile'
    | '/test_avatar_upload'
    | '/artist/new'
    | '/song/$id'
    | '/song/mock'
    | '/profile/$username'
    | '/profile/edit'
    | '/artist/$id/edit'
    | '/artist/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/articles'
    | '/auth'
    | '/feeds'
    | '/library'
    | '/profile'
    | '/test_avatar_upload'
    | '/artist/new'
    | '/song/$id'
    | '/song/mock'
    | '/profile/$username'
    | '/profile/edit'
    | '/artist/$id/edit'
    | '/artist/$id'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/articles'
    | '/auth'
    | '/feeds'
    | '/library'
    | '/(user)/profile'
    | '/(user)/test_avatar_upload'
    | '/artist/new'
    | '/song/$id'
    | '/song/mock'
    | '/(user)/profile_/$username'
    | '/(user)/profile_/edit'
    | '/artist/$id/edit'
    | '/artist/$id/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  ArticlesRoute: typeof ArticlesRoute
  AuthRoute: typeof AuthRoute
  FeedsRoute: typeof FeedsRoute
  LibraryRoute: typeof LibraryRoute
  userProfileRoute: typeof userProfileRoute
  userTest_avatar_uploadRoute: typeof userTest_avatar_uploadRoute
  ArtistNewRoute: typeof ArtistNewRoute
  SongIdRoute: typeof SongIdRoute
  SongMockRoute: typeof SongMockRoute
  userProfileUsernameRoute: typeof userProfileUsernameRoute
  userProfileEditRoute: typeof userProfileEditRoute
  ArtistIdEditRoute: typeof ArtistIdEditRoute
  ArtistIdIndexRoute: typeof ArtistIdIndexRoute
}

declare module '@tanstack/solid-router' {
  interface FileRoutesByPath {
    '/library': {
      id: '/library'
      path: '/library'
      fullPath: '/library'
      preLoaderRoute: typeof LibraryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/feeds': {
      id: '/feeds'
      path: '/feeds'
      fullPath: '/feeds'
      preLoaderRoute: typeof FeedsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/articles': {
      id: '/articles'
      path: '/articles'
      fullPath: '/articles'
      preLoaderRoute: typeof ArticlesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/song/mock': {
      id: '/song/mock'
      path: '/song/mock'
      fullPath: '/song/mock'
      preLoaderRoute: typeof SongMockRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/song/$id': {
      id: '/song/$id'
      path: '/song/$id'
      fullPath: '/song/$id'
      preLoaderRoute: typeof SongIdRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/artist/new': {
      id: '/artist/new'
      path: '/artist/new'
      fullPath: '/artist/new'
      preLoaderRoute: typeof ArtistNewRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(user)/test_avatar_upload': {
      id: '/(user)/test_avatar_upload'
      path: '/test_avatar_upload'
      fullPath: '/test_avatar_upload'
      preLoaderRoute: typeof userTest_avatar_uploadRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(user)/profile': {
      id: '/(user)/profile'
      path: '/profile'
      fullPath: '/profile'
      preLoaderRoute: typeof userProfileRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/artist/$id/': {
      id: '/artist/$id/'
      path: '/artist/$id'
      fullPath: '/artist/$id'
      preLoaderRoute: typeof ArtistIdIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/artist/$id/edit': {
      id: '/artist/$id/edit'
      path: '/artist/$id/edit'
      fullPath: '/artist/$id/edit'
      preLoaderRoute: typeof ArtistIdEditRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(user)/profile_/edit': {
      id: '/(user)/profile_/edit'
      path: '/profile/edit'
      fullPath: '/profile/edit'
      preLoaderRoute: typeof userProfileEditRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/(user)/profile_/$username': {
      id: '/(user)/profile_/$username'
      path: '/profile/$username'
      fullPath: '/profile/$username'
      preLoaderRoute: typeof userProfileUsernameRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  ArticlesRoute: ArticlesRoute,
  AuthRoute: AuthRoute,
  FeedsRoute: FeedsRoute,
  LibraryRoute: LibraryRoute,
  userProfileRoute: userProfileRoute,
  userTest_avatar_uploadRoute: userTest_avatar_uploadRoute,
  ArtistNewRoute: ArtistNewRoute,
  SongIdRoute: SongIdRoute,
  SongMockRoute: SongMockRoute,
  userProfileUsernameRoute: userProfileUsernameRoute,
  userProfileEditRoute: userProfileEditRoute,
  ArtistIdEditRoute: ArtistIdEditRoute,
  ArtistIdIndexRoute: ArtistIdIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
