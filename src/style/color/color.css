@import "./palette.css";

@theme inline {
	--background-color-primary: var(--color-white);
	--background-color-secondary: var(--color-slate-100);
	--background-color-tertiary: var(--color-white);

	--text-color-primary: var(--color-slate-900);
	--text-color-secondary: var(--color-slate-700);
	--text-color-tertiary: var(--color-slate-500);

	--default-border-color: var(--color-slate-300);
	--default-icon-color: var(--color-slate-500);
}

@layer theme {
	[data-mode="dark"] {
		--text-color-primary: var(--color-slate-100);
		--text-color-secondary: var(--color-black);
	}
}
