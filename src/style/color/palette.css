@theme {
	--color-slate-*: initial;
	--color-slate-50: #f6f7f8;
	--color-slate-100: hsl(210 9 96);
	--color-slate-200: hsl(216 12 92);
	--color-slate-300: hsl(220 10 88);
	--color-slate-400: hsl(218 8 73);
	--color-slate-500: hsl(219 8 64);
	--color-slate-600: hsl(221 7 56);
	--color-slate-700: hsl(218 8 48);
	--color-slate-800: hsl(223 10 41);
	--color-slate-900: hsl(223 11 32);
	--color-slate-1000: hsl(222 12 22);
	--color-slate-1100: hsl(223 11 12);

	--color-gray-100: hsl(0 0 95);
	--color-gray-200: hsl(0 0 92);
	--color-gray-300: hsl(0 0 87);
	--color-gray-400: hsl(0 0 73);
	--color-gray-500: hsl(0 0 64);
	--color-gray-600: hsl(0 0 55);
	--color-gray-700: hsl(0 0 46);
	--color-gray-800: hsl(0 0 39);
	--color-gray-900: hsl(0 0 31);
	--color-gray-1000: hsl(0 0 21);
	--color-gray-1100: hsl(0 0 12);
	--color-reimu-100: hsl(4 100 97);
	--color-reimu-200: hsl(6 100 94);
	--color-reimu-300: hsl(5 100 91);
	--color-reimu-400: hsl(3 100 80);
	--color-reimu-500: hsl(1 99 73);
	--color-reimu-600: hsl(0 82 65);
	--color-reimu-700: hsl(357 67 54);
	--color-reimu-800: hsl(356 67 45);
	--color-reimu-900: hsl(354 77 34);
	--color-reimu-1000: hsl(352 92 23);
	--color-reimu-1100: hsl(354 100 14);
	--color-blue-100: hsl(217 100 96);
	--color-blue-200: hsl(218 100 94);
	--color-blue-300: hsl(218 100 90);
	--color-blue-400: hsl(219 100 79);
	--color-blue-500: hsl(221 100 73);
	--color-blue-600: hsl(224 100 67);
	--color-blue-700: hsl(225 84 60);
	--color-blue-800: hsl(226 65 51);
	--color-blue-900: hsl(226 66 41);
	--color-blue-1000: hsl(227 71 30);
	--color-blue-1100: hsl(227 80 17);
	--color-marisa-100: hsl(49 84 90);
	--color-marisa-200: hsl(48 77 85);
	--color-marisa-300: hsl(48 68 79);
	--color-marisa-400: hsl(42 58 63);
	--color-marisa-500: hsl(39 54 53);
	--color-marisa-600: hsl(37 60 45);
	--color-marisa-700: hsl(38 75 36);
	--color-marisa-800: hsl(38 90 28);
	--color-marisa-900: hsl(38 100 21);
	--color-marisa-1000: hsl(37 100 15);
	--color-marisa-1100: hsl(35 100 9);
	--color-green-100: hsl(139 84 93);
	--color-green-200: hsl(140 75 86);
	--color-green-300: hsl(139 68 80);
	--color-green-400: hsl(138 55 58);
	--color-green-500: hsl(138 54 47);
	--color-green-600: hsl(139 71 37);
	--color-green-700: hsl(142 93 28);
	--color-green-800: hsl(142 100 23);
	--color-green-900: hsl(140 100 18);
	--color-green-1000: hsl(137 100 12);
	--color-green-1100: hsl(132 100 7);
}

@layer theme {
	[data-mode="dark"] {
		--color-slate-100: hsl(220 10 6);
		--color-slate-200: hsl(220 11 11);
		--color-slate-300: hsl(220 12 14);
		--color-slate-400: hsl(220 12 25);
		--color-slate-500: hsl(220 11 32);
		--color-slate-600: hsl(221 11 39);
		--color-slate-700: hsl(221 8 47);
		--color-slate-800: hsl(221 7 57);
		--color-slate-900: hsl(220 7 67);
		--color-slate-1000: hsl(222 9 78);
		--color-slate-1100: hsl(220 9 87);
		--color-slate-1200: hsl(220 13 95);
		--color-gray-100: hsl(0 0 6);
		--color-gray-200: hsl(0 0 10);
		--color-gray-300: hsl(0 0 14);
		--color-gray-400: hsl(0 0 24);
		--color-gray-500: hsl(0 0 31);
		--color-gray-600: hsl(0 0 38);
		--color-gray-700: hsl(0 0 45);
		--color-gray-800: hsl(0 0 56);
		--color-gray-900: hsl(0 0 66);
		--color-gray-1000: hsl(0 0 78);
		--color-gray-1100: hsl(0 0 87);
		--color-gray-1200: hsl(0 0 95);
		--color-reimu-100: hsl(358 67 8);
		--color-reimu-200: hsl(359 69 13);
		--color-reimu-300: hsl(357 71 16);
		--color-reimu-400: hsl(358 70 28);
		--color-reimu-500: hsl(358 68 36);
		--color-reimu-600: hsl(358 65 44);
		--color-reimu-700: hsl(358 65 53);
		--color-reimu-800: hsl(357 81 65);
		--color-reimu-900: hsl(357 87 75);
		--color-reimu-1000: hsl(357 90 85);
		--color-reimu-1100: hsl(357 87 91);
		--color-reimu-1200: hsl(355 76 97);
		--color-blue-100: hsl(224 60 9);
		--color-blue-200: hsl(227 59 14);
		--color-blue-300: hsl(226 60 19);
		--color-blue-400: hsl(226 60 32);
		--color-blue-500: hsl(226 59 41);
		--color-blue-600: hsl(226 59 49);
		--color-blue-700: hsl(226 81 59);
		--color-blue-800: hsl(221 100 67);
		--color-blue-900: hsl(214 97 71);
		--color-blue-1000: hsl(210 94 80);
		--color-blue-1100: hsl(209 97 88);
		--color-blue-1200: hsl(207 91 96);
		--color-marisa-100: hsl(38 76 5);
		--color-marisa-200: hsl(37 76 8);
		--color-marisa-300: hsl(37 75 11);
		--color-marisa-400: hsl(38 75 19);
		--color-marisa-500: hsl(37 75 24);
		--color-marisa-600: hsl(38 76 29);
		--color-marisa-700: hsl(37 74 35);
		--color-marisa-800: hsl(38 71 43);
		--color-marisa-900: hsl(41 60 54);
		--color-marisa-1000: hsl(44 64 67);
		--color-marisa-1100: hsl(48 67 79);
		--color-marisa-1200: hsl(50 71 92);
		--color-green-100: hsl(140 82 4);
		--color-green-200: hsl(141 88 6);
		--color-green-300: hsl(139 86 9);
		--color-green-400: hsl(139 87 15);
		--color-green-500: hsl(139 86 19);
		--color-green-600: hsl(139 83 24);
		--color-green-700: hsl(139 80 29);
		--color-green-800: hsl(139 69 38);
		--color-green-900: hsl(140 51 50);
		--color-green-1000: hsl(139 58 66);
		--color-green-1100: hsl(140 66 79);
		--color-green-1200: hsl(139 79 92);
	}
}
