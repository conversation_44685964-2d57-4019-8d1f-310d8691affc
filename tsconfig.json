{
	"compilerOptions": {
		"allowImportingTsExtensions": true,
		"allowJs": true,
		"allowSyntheticDefaultImports": true,
		"esModuleInterop": true,
		"exactOptionalPropertyTypes": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"jsxImportSource": "solid-js",
		"module": "ESNext",
		"moduleResolution": "bundler",
		"noEmit": true,
		"noFallthroughCasesInSwitch": true,
		"noPropertyAccessFromIndexSignature": true,
		"noUncheckedIndexedAccess": true,
		"paths": {
			"~/*": ["./src/*"]
		},
		"skipLibCheck": true,
		// "sourceMap": true,
		"strict": true,
		"target": "ESNext",
		"types": ["vite/client", "@testing-library/jest-dom", "vitest/importMeta"]
	},
	"exclude": ["node_modules", "dist", "public"]
}
